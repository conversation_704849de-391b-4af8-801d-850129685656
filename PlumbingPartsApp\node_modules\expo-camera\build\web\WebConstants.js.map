{"version": 3, "file": "WebConstants.js", "sourceRoot": "", "sources": ["../../src/web/WebConstants.ts"], "names": [], "mappings": "AAEA,qFAAqF;AACrF,MAAM,CAAC,MAAM,mBAAmB,GAAG;IACjC,WAAW,EAAE,IAAI,GAAG,IAAI;IACxB,WAAW,EAAE,IAAI,GAAG,IAAI;IACxB,UAAU,EAAE,IAAI,GAAG,GAAG;IACtB,SAAS,EAAE,GAAG,GAAG,GAAG;IACpB,SAAS,EAAE,GAAG,GAAG,GAAG;CACrB,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAE7D,MAAM,CAAC,MAAM,eAAe,GAA8B;IACxD,GAAG,EAAE,YAAY;IACjB,GAAG,EAAE,WAAW;CACjB,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAA2B;IACxD,KAAK,EAAE,KAAK;IACZ,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA+B;IAChE,KAAK,EAAE,MAAM;IACb,IAAI,EAAE,aAAa;CACpB,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA+B;IAChE,IAAI,EAAE,OAAO;IACb,WAAW,EAAE,MAAM;CACpB,CAAC", "sourcesContent": ["import { CameraType, ImageType } from '../Camera.types';\n\n// https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints/aspectRatio\nexport const VIDEO_ASPECT_RATIOS = {\n  '3840x2160': 3840 / 2160,\n  '1920x1080': 1920 / 1080,\n  '1280x720': 1280 / 720,\n  '640x480': 640 / 480,\n  '352x288': 352 / 288,\n};\n\nexport const PictureSizes = Object.keys(VIDEO_ASPECT_RATIOS);\n\nexport const ImageTypeFormat: Record<ImageType, string> = {\n  jpg: 'image/jpeg',\n  png: 'image/png',\n};\n\nexport const MinimumConstraints: MediaStreamConstraints = {\n  audio: false,\n  video: true,\n};\n\nexport const CameraTypeToFacingMode: Record<CameraType, string> = {\n  front: 'user',\n  back: 'environment',\n};\n\nexport const FacingModeToCameraType: Record<string, CameraType> = {\n  user: 'front',\n  environment: 'back',\n};\n"]}