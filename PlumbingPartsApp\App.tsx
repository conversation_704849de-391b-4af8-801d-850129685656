import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import { AuthProvider } from './src/context/AuthContext';
import { ApiProvider } from './src/context/ApiContext';
import AppNavigator from './src/navigation/AppNavigator';

export default function App() {
  return (
    <ApiProvider>
      <AuthProvider>
        <NavigationContainer>
          <AppNavigator />
          <StatusBar style="auto" />
        </NavigationContainer>
      </AuthProvider>
    </ApiProvider>
  );
}



