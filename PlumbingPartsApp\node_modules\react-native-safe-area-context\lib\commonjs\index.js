"use strict";
'use client';

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _SafeAreaContext = require("./SafeAreaContext");
Object.keys(_SafeAreaContext).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _SafeAreaContext[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _SafeAreaContext[key];
    }
  });
});
var _SafeAreaView = require("./SafeAreaView");
Object.keys(_SafeAreaView).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _SafeAreaView[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _SafeAreaView[key];
    }
  });
});
var _InitialWindow = require("./InitialWindow");
Object.keys(_InitialWindow).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _InitialWindow[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _InitialWindow[key];
    }
  });
});
var _SafeArea = require("./SafeArea.types");
Object.keys(_SafeArea).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _SafeArea[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _SafeArea[key];
    }
  });
});
//# sourceMappingURL=index.js.map