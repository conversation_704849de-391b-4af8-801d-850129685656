import React, { createContext, useContext, ReactNode } from 'react';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import Constants from 'expo-constants';

// API Configuration
const API_BASE_URL = __DEV__
  ? 'http://localhost:3000/v1'
  : 'https://api.plumbingparts.app/v1';

// Check if we're in offline mode
const isOfflineMode = __DEV__ && process.env.EXPO_PUBLIC_OFFLINE_MODE === 'true';

interface ApiContextType {
  api: AxiosInstance;
  setAuthToken: (token: string | null) => void;
  clearAuthToken: () => void;
  isOffline: boolean;
}

const ApiContext = createContext<ApiContextType | undefined>(undefined);

export const useApi = () => {
  const context = useContext(ApiContext);
  if (!context) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  return context;
};

interface ApiProviderProps {
  children: ReactNode;
}

export const ApiProvider: React.FC<ApiProviderProps> = ({ children }) => {
  // Create axios instance
  const api = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Add request interceptor to handle offline mode
  api.interceptors.request.use(
    (config) => {
      if (isOfflineMode) {
        console.log('🔌 Offline mode: API call intercepted:', config.url);
        return Promise.reject(new Error('OFFLINE_MODE'));
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Request interceptor to add auth token
  api.interceptors.request.use(
    async (config) => {
      try {
        const token = await AsyncStorage.getItem('accessToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      } catch (error) {
        console.error('Error getting auth token:', error);
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor to handle token refresh
  api.interceptors.response.use(
    (response: AxiosResponse) => {
      return response;
    },
    async (error) => {
      const originalRequest = error.config;

      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        try {
          const refreshToken = await AsyncStorage.getItem('refreshToken');
          if (refreshToken) {
            const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
              refreshToken,
            });

            const { accessToken, refreshToken: newRefreshToken } = response.data.data.tokens;
            
            await AsyncStorage.setItem('accessToken', accessToken);
            await AsyncStorage.setItem('refreshToken', newRefreshToken);

            // Retry original request with new token
            originalRequest.headers.Authorization = `Bearer ${accessToken}`;
            return api(originalRequest);
          }
        } catch (refreshError) {
          // Refresh failed, clear tokens and redirect to login
          await AsyncStorage.multiRemove(['accessToken', 'refreshToken', 'user']);
          console.error('Token refresh failed:', refreshError);
        }
      }

      return Promise.reject(error);
    }
  );

  const setAuthToken = async (token: string | null) => {
    if (token) {
      await AsyncStorage.setItem('accessToken', token);
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      await AsyncStorage.removeItem('accessToken');
      delete api.defaults.headers.common['Authorization'];
    }
  };

  const clearAuthToken = async () => {
    await AsyncStorage.multiRemove(['accessToken', 'refreshToken', 'user']);
    delete api.defaults.headers.common['Authorization'];
  };

  const value: ApiContextType = {
    api,
    setAuthToken,
    clearAuthToken,
    isOffline: isOfflineMode,
  };

  return (
    <ApiContext.Provider value={value}>
      {children}
    </ApiContext.Provider>
  );
};
