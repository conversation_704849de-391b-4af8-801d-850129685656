import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useApi } from './ApiContext';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  role: string;
  createdAt: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  companyName?: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { api, setAuthToken, clearAuthToken, isOffline } = useApi();

  const isAuthenticated = !!user;

  // Load user from storage on app start
  useEffect(() => {
    loadUserFromStorage();
  }, []);

  const loadUserFromStorage = async () => {
    try {
      setIsLoading(true);
      const [storedUser, accessToken] = await AsyncStorage.multiGet([
        'user',
        'accessToken'
      ]);

      if (storedUser[1] && accessToken[1]) {
        const userData = JSON.parse(storedUser[1]);
        setUser(userData);
        await setAuthToken(accessToken[1]);
        
        // Verify token is still valid by fetching user profile
        try {
          await refreshUser();
        } catch (error) {
          // Token invalid, clear auth data
          await logout();
        }
      }
    } catch (error) {
      console.error('Error loading user from storage:', error);
      await logout();
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (credentials: LoginCredentials) => {
    try {
      setIsLoading(true);

      // Handle offline mode
      if (isOffline) {
        // For demo purposes, create a mock user
        const mockUser: User = {
          id: 'demo-user-id',
          email: credentials.email,
          firstName: 'Demo',
          lastName: 'User',
          role: 'user',
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        await AsyncStorage.setItem('user', JSON.stringify(mockUser));
        setUser(mockUser);
        return;
      }

      const response = await api.post('/auth/login', credentials);

      if (response.data.success) {
        const { user: userData, tokens } = response.data.data;

        // Store user and tokens
        await AsyncStorage.multiSet([
          ['user', JSON.stringify(userData)],
          ['accessToken', tokens.accessToken],
          ['refreshToken', tokens.refreshToken]
        ]);

        setUser(userData);
        await setAuthToken(tokens.accessToken);
      } else {
        throw new Error(response.data.error?.message || 'Login failed');
      }
    } catch (error: any) {
      console.error('Login error:', error);

      // Handle network errors gracefully
      if (error.message === 'OFFLINE_MODE' || error.code === 'ECONNREFUSED' || error.code === 'NETWORK_ERROR') {
        throw new Error('Unable to connect to server. Please check your internet connection.');
      }

      const message = error.response?.data?.error?.message || error.message || 'Login failed';
      throw new Error(message);
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (data: RegisterData) => {
    try {
      setIsLoading(true);
      const response = await api.post('/auth/register', data);
      
      if (response.data.success) {
        const { user: userData, tokens } = response.data.data;
        
        // Store user and tokens
        await AsyncStorage.multiSet([
          ['user', JSON.stringify(userData)],
          ['accessToken', tokens.accessToken],
          ['refreshToken', tokens.refreshToken]
        ]);
        
        setUser(userData);
        await setAuthToken(tokens.accessToken);
      } else {
        throw new Error(response.data.error?.message || 'Registration failed');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      const message = error.response?.data?.error?.message || error.message || 'Registration failed';
      throw new Error(message);
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      
      // Call logout endpoint if user is authenticated
      if (user) {
        try {
          await api.post('/auth/logout');
        } catch (error) {
          // Continue with logout even if API call fails
          console.error('Logout API call failed:', error);
        }
      }
      
      // Clear local storage and state
      await clearAuthToken();
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUser = async () => {
    try {
      const response = await api.get('/auth/profile');
      
      if (response.data.success) {
        const userData = response.data.data.user;
        await AsyncStorage.setItem('user', JSON.stringify(userData));
        setUser(userData);
      } else {
        throw new Error('Failed to refresh user data');
      }
    } catch (error) {
      console.error('Refresh user error:', error);
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
