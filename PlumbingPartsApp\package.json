{"name": "plumbingpartsapp", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.0.4", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "axios": "^1.10.0", "expo": "~53.0.13", "expo-camera": "^16.1.9", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-media-library": "^17.1.7", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.11.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}