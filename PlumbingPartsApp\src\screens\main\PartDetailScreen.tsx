import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
  Dimensions,
  Share,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';

import { RootStackParamList } from '../../navigation/AppNavigator';
import { useApi } from '../../context/ApiContext';
import colors from '../../theme/colors';

const { width } = Dimensions.get('window');

type PartDetailRouteProp = RouteProp<RootStackParamList, 'PartDetail'>;

interface PartDetail {
  id: string;
  name: string;
  description: string;
  category: string;
  subcategory: string;
  specifications: Record<string, string>;
  images: string[];
  compatibleParts: string[];
  installationNotes: string;
}

interface Supplier {
  id: string;
  name: string;
  inStock: boolean;
  quantity: number;
  price: number;
  currency: string;
  leadTime: string;
  minimumOrder: number;
  lastUpdated: string;
}

// Mock data for demonstration
const mockPartDetail: PartDetail = {
  id: '1',
  name: '1/2 inch PVC Elbow',
  description: '90-degree PVC elbow fitting for 1/2 inch pipe connections. Ideal for residential and commercial plumbing applications.',
  category: 'Fittings',
  subcategory: 'Elbows',
  specifications: {
    'Material': 'PVC',
    'Size': '1/2 inch',
    'Angle': '90 degrees',
    'Connection Type': 'Socket',
    'Pressure Rating': '200 PSI',
    'Temperature Range': '32-140°F',
  },
  images: [
    'https://via.placeholder.com/300x300/007AFF/FFFFFF?text=PVC+Elbow',
    'https://via.placeholder.com/300x300/007AFF/FFFFFF?text=Side+View',
  ],
  compatibleParts: ['1/2 inch PVC Pipe', 'PVC Primer', 'PVC Cement'],
  installationNotes: 'Use PVC primer and cement for permanent installation. Ensure surfaces are clean and dry before application.',
};

const mockSuppliers: Supplier[] = [
  {
    id: '1',
    name: 'ABC Supply Co',
    inStock: true,
    quantity: 150,
    price: 2.45,
    currency: 'USD',
    leadTime: '1-2 business days',
    minimumOrder: 1,
    lastUpdated: '2024-01-15T14:30:00Z',
  },
  {
    id: '2',
    name: 'Home Depot',
    inStock: true,
    quantity: 89,
    price: 2.89,
    currency: 'USD',
    leadTime: 'Same day pickup',
    minimumOrder: 1,
    lastUpdated: '2024-01-15T14:25:00Z',
  },
  {
    id: '3',
    name: 'Ferguson',
    inStock: false,
    quantity: 0,
    price: 2.38,
    currency: 'USD',
    leadTime: '5-7 business days',
    minimumOrder: 10,
    lastUpdated: '2024-01-15T14:20:00Z',
  },
];

const PartDetailScreen = () => {
  const route = useRoute<PartDetailRouteProp>();
  const navigation = useNavigation();
  const { api } = useApi();
  const { partId } = route.params;

  const [part, setPart] = useState<PartDetail | null>(null);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isFavorite, setIsFavorite] = useState(false);
  const [showAllSuppliers, setShowAllSuppliers] = useState(false);

  useEffect(() => {
    loadPartDetails();
  }, [partId]);

  const loadPartDetails = async () => {
    try {
      setLoading(true);
      
      // In a real app, these would be API calls
      // const partResponse = await api.get(`/parts/${partId}`);
      // const suppliersResponse = await api.get(`/parts/${partId}/availability`);
      
      // For demo, use mock data
      setTimeout(() => {
        setPart(mockPartDetail);
        setSuppliers(mockSuppliers);
        setLoading(false);
      }, 1000);
      
    } catch (error) {
      console.error('Error loading part details:', error);
      Alert.alert('Error', 'Failed to load part details');
      setLoading(false);
    }
  };

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
    // In a real app, this would make an API call to save/remove favorite
  };

  const handlePriceAlert = (supplier: Supplier) => {
    Alert.alert(
      'Price Alert',
      `Set a price alert for ${part?.name} from ${supplier.name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Set Alert', onPress: () => {} },
      ]
    );
  };

  const handleShare = async () => {
    if (!part) return;

    try {
      await Share.share({
        message: `Check out this plumbing part: ${part.name}\n\n${part.description}\n\nBest price: $${getBestPrice()?.price || 'N/A'}`,
        title: part.name,
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const getBestPrice = () => {
    const inStockSuppliers = suppliers.filter(s => s.inStock);
    if (inStockSuppliers.length === 0) return suppliers[0];
    return inStockSuppliers.reduce((min, supplier) =>
      supplier.price < min.price ? supplier : min
    );
  };

  const getSupplierStatusColor = (supplier: Supplier) => {
    if (!supplier.inStock) return colors.error;
    if (supplier.quantity < 10) return colors.warning;
    return colors.success;
  };

  const formatLastUpdated = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just updated';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading part details...</Text>
      </View>
    );
  }

  if (!part) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle" size={64} color="#ff4444" />
        <Text style={styles.errorText}>Part not found</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>Part Details</Text>

        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleShare}
          >
            <Ionicons name="share-outline" size={24} color={colors.text.primary} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Image Gallery */}
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: part.images[selectedImageIndex] }}
            style={styles.mainImage}
            resizeMode="cover"
          />
          
          <TouchableOpacity style={styles.favoriteButton} onPress={toggleFavorite}>
            <Ionicons
              name={isFavorite ? 'heart' : 'heart-outline'}
              size={24}
              color={isFavorite ? '#ff4444' : '#fff'}
            />
          </TouchableOpacity>
          
          {part.images.length > 1 && (
            <ScrollView
              horizontal
              style={styles.thumbnailContainer}
              showsHorizontalScrollIndicator={false}
            >
              {part.images.map((image, index) => (
                <TouchableOpacity
                  key={index}
                  onPress={() => setSelectedImageIndex(index)}
                >
                  <Image
                    source={{ uri: image }}
                    style={[
                      styles.thumbnail,
                      selectedImageIndex === index && styles.thumbnailSelected,
                    ]}
                  />
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}
        </View>

        {/* Part Information */}
        <View style={styles.infoContainer}>
          <Text style={styles.partName}>{part.name}</Text>
          <Text style={styles.partCategory}>
            {part.category} • {part.subcategory}
          </Text>
          <Text style={styles.partDescription}>{part.description}</Text>
        </View>

        {/* Specifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Specifications</Text>
          <View style={styles.specificationsContainer}>
            {Object.entries(part.specifications).map(([key, value]) => (
              <View key={key} style={styles.specificationRow}>
                <Text style={styles.specificationKey}>{key}</Text>
                <Text style={styles.specificationValue}>{value}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Suppliers & Pricing */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Suppliers & Pricing</Text>
          {suppliers.map((supplier) => (
            <View key={supplier.id} style={styles.supplierCard}>
              <View style={styles.supplierHeader}>
                <Text style={styles.supplierName}>{supplier.name}</Text>
                <View style={styles.supplierStatus}>
                  <View
                    style={[
                      styles.statusDot,
                      { backgroundColor: supplier.inStock ? '#28A745' : '#ff4444' },
                    ]}
                  />
                  <Text
                    style={[
                      styles.statusText,
                      { color: supplier.inStock ? '#28A745' : '#ff4444' },
                    ]}
                  >
                    {supplier.inStock ? 'In Stock' : 'Out of Stock'}
                  </Text>
                </View>
              </View>
              
              <View style={styles.supplierDetails}>
                <View style={styles.priceContainer}>
                  <Text style={styles.price}>
                    ${supplier.price.toFixed(2)}
                  </Text>
                  <Text style={styles.currency}>{supplier.currency}</Text>
                </View>
                
                <View style={styles.supplierInfo}>
                  <Text style={styles.infoText}>
                    Lead Time: {supplier.leadTime}
                  </Text>
                  <Text style={styles.infoText}>
                    Min Order: {supplier.minimumOrder}
                  </Text>
                  {supplier.inStock && (
                    <Text style={styles.infoText}>
                      Qty Available: {supplier.quantity}
                    </Text>
                  )}
                </View>
              </View>
              
              <View style={styles.supplierActions}>
                <TouchableOpacity
                  style={styles.alertButton}
                  onPress={() => handlePriceAlert(supplier)}
                >
                  <Ionicons name="notifications-outline" size={16} color="#007AFF" />
                  <Text style={styles.alertButtonText}>Price Alert</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.contactButton}>
                  <Text style={styles.contactButtonText}>Contact Supplier</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>

        {/* Installation Notes */}
        {part.installationNotes && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Installation Notes</Text>
            <View style={styles.notesContainer}>
              <Ionicons name="information-circle" size={20} color="#007AFF" />
              <Text style={styles.notesText}>{part.installationNotes}</Text>
            </View>
          </View>
        )}

        {/* Compatible Parts */}
        {part.compatibleParts.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Compatible Parts</Text>
            <View style={styles.compatibleContainer}>
              {part.compatibleParts.map((compatiblePart, index) => (
                <View key={index} style={styles.compatibleItem}>
                  <Text style={styles.compatibleText}>{compatiblePart}</Text>
                </View>
              ))}
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },

  // Header styles
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: colors.surface.primary,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
    elevation: 2,
    shadowColor: colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.surface.secondary,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.primary,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
  },
  loadingText: {
    fontSize: 16,
    color: colors.text.secondary,
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
  },
  errorText: {
    fontSize: 18,
    color: colors.error,
    marginTop: 16,
  },
  imageContainer: {
    backgroundColor: '#fff',
    position: 'relative',
  },
  mainImage: {
    width: '100%',
    height: 300,
  },
  favoriteButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  thumbnailContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  thumbnail: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 8,
    opacity: 0.7,
  },
  thumbnailSelected: {
    opacity: 1,
    borderWidth: 2,
    borderColor: '#007AFF',
  },
  infoContainer: {
    backgroundColor: '#fff',
    padding: 20,
    marginTop: 8,
  },
  partName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  partCategory: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
    marginBottom: 12,
  },
  partDescription: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  section: {
    backgroundColor: '#fff',
    marginTop: 8,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  specificationsContainer: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  specificationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  specificationKey: {
    fontSize: 14,
    color: '#666',
    fontWeight: '600',
  },
  specificationValue: {
    fontSize: 14,
    color: '#333',
  },
  supplierCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  supplierHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  supplierName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  supplierStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  supplierDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  price: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  currency: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  supplierInfo: {
    flex: 1,
    marginLeft: 16,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  supplierActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  alertButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  alertButtonText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '600',
    marginLeft: 4,
  },
  contactButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  contactButtonText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '600',
  },
  notesContainer: {
    flexDirection: 'row',
    backgroundColor: '#f0f8ff',
    padding: 16,
    borderRadius: 8,
  },
  notesText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 12,
    flex: 1,
    lineHeight: 20,
  },
  compatibleContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  compatibleItem: {
    backgroundColor: '#f0f8ff',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  compatibleText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
});

export default PartDetailScreen;
