# API Configuration Modes

This app can run in two modes:

## 1. Online Mode (Default)
- Connects to the backend API server
- Full functionality with real data
- Requires backend server to be running on localhost:3000

### To run in online mode:
```bash
# Start the backend server first
cd ../backend
npm start

# Then start the mobile app
cd ../PlumbingPartsApp
npm start
```

## 2. Offline Mode (Demo)
- Works without backend server
- Uses mock data for demonstration
- Perfect for testing UI/UX without server setup

### To run in offline mode:
```bash
# Set environment variable
export EXPO_PUBLIC_OFFLINE_MODE=true

# Start the mobile app
npm start
```

Or create a `.env` file in the PlumbingPartsApp directory:
```
EXPO_PUBLIC_OFFLINE_MODE=true
```

## Current Status
- ✅ Backend server is running on port 3000 (without database)
- ✅ Mobile app handles API connection errors gracefully
- ✅ Offline mode provides mock data for testing
- ✅ System status shows offline indicator when appropriate

## Features Available in Offline Mode
- ✅ Camera functionality
- ✅ Mock part identification
- ✅ Navigation between screens
- ✅ UI/UX testing
- ❌ Real authentication
- ❌ Real part data
- ❌ Supplier integration

## Troubleshooting
If you see API provider errors:
1. Check if backend server is running
2. Enable offline mode for demo purposes
3. Check network connectivity
4. Verify API endpoints in ApiContext.tsx
