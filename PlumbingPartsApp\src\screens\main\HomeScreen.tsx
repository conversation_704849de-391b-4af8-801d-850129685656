import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import colors from '../../theme/colors';
import SystemStatusHeader, { SystemAlert } from '../../components/SystemStatusHeader';

const { width, height } = Dimensions.get('window');

interface HomeScreenProps {
  navigation: {
    navigate: (screen: string) => void;
  };
}

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  // Sample alerts data
  const sampleAlerts: SystemAlert[] = [
    {
      id: '1',
      type: 'critical',
      message: 'Water pump requires immediate attention',
      component: 'Water Pump',
      timestamp: '2024-06-30T10:30:00Z'
    },
    {
      id: '2',
      type: 'warning',
      message: 'Bathroom sink has slow drain',
      component: 'Bathroom Sink 2',
      timestamp: '2024-06-30T09:15:00Z'
    }
  ];

  const handleMenuPress = () => {
    Alert.alert('Menu', 'Menu functionality coming soon!');
  };

  const handleAlertsPress = () => {
    Alert.alert('Alerts', `You have ${sampleAlerts.length} system alerts.`);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* System Status Header */}
      <SystemStatusHeader
        systemName="PlumbMaster Home"
        alerts={sampleAlerts}
        onMenuPress={handleMenuPress}
        onAlertsPress={handleAlertsPress}
        onSystemNamePress={() => Alert.alert('System', 'System selection coming soon!')}
      />

      {/* Plumbing System Visual */}
      <View style={styles.plumbingSystemContainer}>
        <View style={styles.plumbingPlaceholder}>
          <Ionicons name="construct" size={80} color={colors.primary} />
          <Text style={styles.placeholderText}>Plumbing System Overview</Text>
          <Text style={styles.placeholderSubtext}>Visual diagram coming soon</Text>
        </View>
      </View>

      {/* Bottom Action Bar */}
      <View style={styles.bottomActionBar}>
        <TouchableOpacity
          style={styles.actionBarButton}
          onPress={() => navigation.navigate('Camera')}
        >
          <Ionicons name="camera" size={24} color={colors.primary} />
          <Text style={styles.actionBarButtonText}>Scan</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionBarButton, styles.primaryAction]}
          onPress={() => Alert.alert('Diagnose', 'Running system diagnostic...')}
        >
          <Ionicons name="search" size={24} color={colors.text.inverse} />
          <Text style={[styles.actionBarButtonText, { color: colors.text.inverse }]}>
            Diagnose System
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionBarButton}
          onPress={() => navigation.navigate('Profile')}
        >
          <Ionicons name="settings" size={24} color={colors.primary} />
          <Text style={styles.actionBarButtonText}>Tools</Text>
        </TouchableOpacity>
      </View>

    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  plumbingSystemContainer: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  plumbingPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  placeholderText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginTop: 20,
    textAlign: 'center',
  },
  placeholderSubtext: {
    fontSize: 16,
    color: colors.text.secondary,
    marginTop: 8,
    textAlign: 'center',
  },
  bottomActionBar: {
    flexDirection: 'row',
    backgroundColor: colors.surface.primary,
    paddingHorizontal: 20,
    paddingVertical: 15,
    paddingBottom: 25,
    borderTopWidth: 1,
    borderTopColor: colors.border.light,
    elevation: 8,
    shadowColor: colors.shadow.dark,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  actionBarButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 12,
    marginHorizontal: 5,
  },
  primaryAction: {
    backgroundColor: colors.primary,
    flex: 2,
  },
  actionBarButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.text.secondary,
    marginTop: 4,
  },


});

export default HomeScreen;

