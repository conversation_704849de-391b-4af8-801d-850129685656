{"version": 3, "file": "ExpoCamera.js", "sourceRoot": "", "sources": ["../src/ExpoCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,wBAAwB,EAAE,MAAM,mBAAmB,CAAC;AAK7D,MAAM,UAAU,GAAqC,wBAAwB,CAAC,YAAY,CAAC,CAAC;AAE5F,eAAe,UAAU,CAAC", "sourcesContent": ["import { requireNativeViewManager } from 'expo-modules-core';\nimport { type ComponentType } from 'react';\n\nimport { CameraNativeProps } from './Camera.types';\n\nconst ExpoCamera: ComponentType<CameraNativeProps> = requireNativeViewManager('ExpoCamera');\n\nexport default ExpoCamera;\n"]}