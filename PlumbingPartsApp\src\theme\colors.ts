// Color theme inspired by the plumbing parts hero image
// Warm, professional, and engaging color palette

const colors = {
  // Primary colors from the hero image
  primary: '#B8860B',        // Warm copper/brass tone
  primaryDark: '#8B6914',    // Darker copper
  primaryLight: '#DAA520',   // Lighter gold
  
  // Secondary colors
  secondary: '#4682B4',      // Steel blue (from pipes)
  secondaryDark: '#2F4F4F',  // Dark slate gray
  secondaryLight: '#87CEEB', // Sky blue
  
  // Accent colors
  accent: '#CD853F',         // Peru/bronze tone
  accentLight: '#DEB887',    // Burlywood
  success: '#228B22',        // Forest green
  warning: '#FF8C00',        // Dark orange
  error: '#DC143C',          // Crimson
  
  // Background colors inspired by the warm brown backdrop
  background: {
    primary: '#F5F5DC',      // Beige (warm neutral)
    secondary: '#FAEBD7',    // Antique white
    tertiary: '#FFF8DC',     // Cornsilk
    dark: '#8B4513',         // Saddle brown (from image background)
  },
  
  // Surface colors
  surface: {
    primary: '#FFFFFF',      // Pure white (like fixtures)
    secondary: '#F8F8FF',    // Ghost white
    elevated: '#FFFAFA',     // Snow
    overlay: 'rgba(139, 69, 19, 0.1)', // Warm brown overlay
  },
  
  // Text colors
  text: {
    primary: '#2F2F2F',      // Dark gray
    secondary: '#5D5D5D',    // Medium gray
    tertiary: '#8B8B8B',     // Light gray
    inverse: '#FFFFFF',      // White text
    accent: '#B8860B',       // Copper text
  },
  
  // Border and divider colors
  border: {
    light: '#E0E0E0',        // Light gray
    medium: '#CCCCCC',       // Medium gray
    dark: '#999999',         // Dark gray
    accent: '#DAA520',       // Gold border
  },
  
  // Shadow colors
  shadow: {
    light: 'rgba(0, 0, 0, 0.1)',
    medium: 'rgba(0, 0, 0, 0.2)',
    dark: 'rgba(0, 0, 0, 0.3)',
    colored: 'rgba(184, 134, 11, 0.2)', // Copper shadow
  },
  
  // Status colors
  status: {
    online: '#32CD32',       // Lime green
    offline: '#DC143C',      // Crimson
    pending: '#FF8C00',      // Dark orange
    inactive: '#A9A9A9',     // Dark gray
  },
  
  // Gradient colors for engaging effects
  gradients: {
    primary: ['#DAA520', '#B8860B'],     // Gold to copper
    secondary: ['#87CEEB', '#4682B4'],   // Light to steel blue
    warm: ['#FFF8DC', '#F5F5DC'],        // Warm neutrals
    hero: ['rgba(139, 69, 19, 0.8)', 'rgba(184, 134, 11, 0.6)'], // Hero overlay
  },
  
  // Legacy colors for compatibility (use surface.primary and text.primary instead)
  white: '#FFFFFF',
  black: '#000000',
  
  // Interactive states
  interactive: {
    hover: 'rgba(184, 134, 11, 0.1)',
    pressed: 'rgba(184, 134, 11, 0.2)',
    focused: 'rgba(184, 134, 11, 0.3)',
    disabled: '#D3D3D3',
  },
};

export default colors;
