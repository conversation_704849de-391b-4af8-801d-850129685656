{"formatVersion": "1.1", "component": {"group": "host.exp.exponent", "module": "expo.modules.camera", "version": "16.1.9", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.13"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.exifinterface", "module": "exifinterface", "version": {"requires": "1.3.7"}}, {"group": "androidx.appcompat", "module": "appcompat", "version": {"requires": "1.1.0"}}], "files": [{"name": "expo.modules.camera-16.1.9.aar", "url": "expo.modules.camera-16.1.9.aar", "size": 236313, "sha512": "c4116c83435fc7e96e177f6a4de10bc24f9a4a6a7dbe68d37c82f9e8ba7bba2f4e0ea027abe935551e76f6ba9e524858b297ec4f3641195e707d71d9dc9cde22", "sha256": "398901de76f48c1305fd7db8e113d2d185d7e1d70c6fa3a30b7c384110ac97f8", "sha1": "5be531ce8becee4c11ffee340001895bc4b10236", "md5": "78859536571b94e485f341f5e9f0d278"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7", "version": {"requires": "2.0.21"}}, {"group": "androidx.camera", "module": "camera-core", "version": {"requires": "1.4.1"}}, {"group": "androidx.camera", "module": "camera-camera2", "version": {"requires": "1.4.1"}}, {"group": "androidx.camera", "module": "camera-lifecycle", "version": {"requires": "1.4.1"}}, {"group": "androidx.camera", "module": "camera-video", "version": {"requires": "1.4.1"}}, {"group": "com.google.android.gms", "module": "play-services-code-scanner", "version": {"requires": "16.1.0"}}, {"group": "androidx.camera", "module": "camera-view", "version": {"requires": "1.4.1"}}, {"group": "androidx.camera", "module": "camera-extensions", "version": {"requires": "1.4.1"}}, {"group": "com.google.mlkit", "module": "barcode-scanning", "version": {"requires": "17.2.0"}}, {"group": "androidx.camera", "module": "camera-mlkit-vision", "version": {"requires": "1.4.1"}}, {"group": "androidx.exifinterface", "module": "exifinterface", "version": {"requires": "1.3.7"}}, {"group": "androidx.appcompat", "module": "appcompat", "version": {"requires": "1.1.0"}}], "files": [{"name": "expo.modules.camera-16.1.9.aar", "url": "expo.modules.camera-16.1.9.aar", "size": 236313, "sha512": "c4116c83435fc7e96e177f6a4de10bc24f9a4a6a7dbe68d37c82f9e8ba7bba2f4e0ea027abe935551e76f6ba9e524858b297ec4f3641195e707d71d9dc9cde22", "sha256": "398901de76f48c1305fd7db8e113d2d185d7e1d70c6fa3a30b7c384110ac97f8", "sha1": "5be531ce8becee4c11ffee340001895bc4b10236", "md5": "78859536571b94e485f341f5e9f0d278"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "expo.modules.camera-16.1.9-sources.jar", "url": "expo.modules.camera-16.1.9-sources.jar", "size": 27684, "sha512": "106e7de0a236c4c8afb31793bd64be2e5366b05d356708bbf9b3432e06cad731384cfa078f49e8c4c66a38138c3574796532bf12486c917e0589be9c86d44cbf", "sha256": "7802bffa9f225729a299c3f1e0cbb596bea3df70ffa0f944087aec2c11fda7ea", "sha1": "4a6a4e3badcd1ee6bd1a76841bf6dffc9fedbaf5", "md5": "461805a7bf04a7c8fca69f78e0b9bc85"}]}]}