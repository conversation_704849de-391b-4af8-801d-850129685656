{"version": 3, "file": "ExpoCamera.web.js", "sourceRoot": "", "sources": ["../src/ExpoCamera.web.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAC/C,OAAO,EAEL,MAAM,EACN,OAAO,EACP,mBAAmB,GAGpB,MAAM,OAAO,CAAC;AACf,OAAO,EAAa,UAAU,EAAE,IAAI,EAAa,MAAM,cAAc,CAAC;AACtE,OAAO,aAAa,MAAM,6CAA6C,CAAC;AAQxE,OAAO,aAAa,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AAC9D,OAAO,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAC;AASxD,MAAM,cAAc,GAAG,CAAC,EACtB,MAAM,EACN,MAAM,EACN,GAAG,EACH,GAAG,KAAK,EAC6B,EAAE,EAAE;IACzC,MAAM,KAAK,GAAG,MAAM,CAA0B,IAAI,CAAC,CAAC;IAEpD,MAAM,MAAM,GAAG,kBAAkB,CAAC,KAAK,EAAE,MAAoB,EAAE,KAAK,EAAE;QACpE,aAAa;YACX,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;gBACxB,KAAK,CAAC,aAAa,EAAE,CAAC;YACxB,CAAC;QACH,CAAC;QACD,YAAY,EAAE,KAAK,CAAC,YAAY;KACjC,CAAC,CAAC;IAEH,MAAM,kBAAkB,GAAG,OAAO,CAAU,GAAG,EAAE;QAC/C,OAAO,OAAO,CACZ,KAAK,CAAC,sBAAsB,EAAE,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,gBAAgB,CACvF,CAAC;IACJ,CAAC,EAAE,CAAC,KAAK,CAAC,sBAAsB,EAAE,YAAY,EAAE,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAEzE,eAAe,CAAC,KAAK,EAAE;QACrB,QAAQ,EAAE,GAAG;QACb,SAAS,EAAE,kBAAkB;QAC7B,cAAc,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,aAAa,EAAE,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE;QACpE,SAAS,CAAC,KAAK;YACb,IAAI,KAAK,CAAC,gBAAgB,EAAE,CAAC;gBAC3B,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;KACF,CAAC,CAAC;IAEH,mBAAmB,CACjB,GAAG,EACH,GAAG,EAAE,CAAC,CAAC;QACL,KAAK,CAAC,wBAAwB;YAC5B,OAAO,YAAY,CAAC;QACtB,CAAC;QACD,KAAK,CAAC,WAAW,CAAC,OAA6B;YAC7C,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE,UAAU,KAAK,KAAK,CAAC,OAAO,EAAE,gBAAgB,EAAE,CAAC;gBACpF,MAAM,IAAI,UAAU,CAClB,sBAAsB,EACtB,8EAA8E,CAC/E,CAAC;YACJ,CAAC;YACD,MAAM,QAAQ,GAAG,MAAM,CAAC,kBAAkB,CAAC;YAC3C,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,UAAU,CAAC,sBAAsB,EAAE,+BAA+B,CAAC,CAAC;YAChF,CAAC;YAED,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE;gBACtC,GAAG,OAAO;gBACV,6IAA6I;gBAC7I,cAAc,CAAC,OAAO;oBACpB,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;wBAC3B,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;oBAClC,CAAC;oBACD,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;wBACzB,KAAK,CAAC,cAAc,CAAC,EAAE,WAAW,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;oBACnE,CAAC;gBACH,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QACD,KAAK,CAAC,aAAa;YACjB,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBAClB,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACvB,CAAC;QACH,CAAC;QACD,KAAK,CAAC,YAAY;YAChB,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBAClB,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACxB,CAAC;QACH,CAAC;QACD,KAAK,CAAC,aAAa;YACjB,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACzD,CAAC;QACD,KAAK,CAAC,MAAM;YACV,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAChD,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;QACrB,CAAC;QACD,KAAK,CAAC,eAAe;YACnB,OAAO,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QAC3D,CAAC;QACD,KAAK,CAAC,mBAAmB;YACvB,OAAO,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAC/D,CAAC;QACD,KAAK,CAAC,kBAAkB;YACtB,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YAC5D,OAAO,EAAE,CAAC;QACZ,CAAC;KACF,CAAC,EACF,CAAC,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC,cAAc,CAAC,CAClD,CAAC;IAEF,qGAAqG;IACrG,iHAAiH;IACjH,MAAM,OAAO,GAAG,IAAI,CAAC;IAErB,MAAM,KAAK,GAAG,OAAO,CAAuB,GAAG,EAAE;QAC/C,MAAM,mBAAmB,GAAG,MAAM,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;QACrE,OAAO;YACL,UAAU,CAAC,YAAY;YACvB,MAAM,CAAC,KAAK;YACZ;gBACE,kBAAkB;gBAClB,SAAS,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS;aAC9D;SACF,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAElB,OAAO,CACL,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CACvE;MAAA,CAAC,KAAK,CACJ,QAAQ,CACR,WAAW,CACX,KAAK,CAAC,CAAC,OAAO,CAAC,CACf,MAAM,CAAC,CAAC,MAAM,CAAC,CACf,aAAa,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,CACnC,GAAG,CAAC,CAAC,KAAK,CAAC,CACX,KAAK,CAAC,CAAC,KAAK,CAAC,EAEf;MAAA,CAAC,KAAK,CAAC,QAAQ,CACjB;IAAA,EAAE,IAAI,CAAC,CACR,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,cAAc,CAAC;AAE9B,MAAM,KAAK,GAAG,CACZ,KAMC,EACD,EAAE,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC;AAE1C,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;IAC/B,YAAY,EAAE;QACZ,IAAI,EAAE,CAAC;QACP,UAAU,EAAE,SAAS;KACtB;IACD,KAAK,EAAE;QACL,KAAK,EAAE,MAAM;QACb,MAAM,EAAE,MAAM;QACd,SAAS,EAAE,OAAO;KACnB;CACF,CAAC,CAAC", "sourcesContent": ["import { CodedError } from 'expo-modules-core';\nimport {\n  type PropsWithChildren,\n  useRef,\n  useMemo,\n  useImperativeHandle,\n  type ComponentProps,\n  type Ref,\n} from 'react';\nimport { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';\nimport createElement from 'react-native-web/dist/exports/createElement';\n\nimport {\n  CameraNativeProps,\n  CameraCapturedPicture,\n  CameraPictureOptions,\n  CameraType,\n} from './Camera.types';\nimport CameraManager from './ExpoCameraManager.web';\nimport { capture } from './web/WebCameraUtils';\nimport { PictureSizes } from './web/WebConstants';\nimport { useWebCameraStream } from './web/useWebCameraStream';\nimport { useWebQRScanner } from './web/useWebQRScanner';\n\nexport interface ExponentCameraRef {\n  getAvailablePictureSizes: (ratio: string) => Promise<string[]>;\n  takePicture: (options: CameraPictureOptions) => Promise<CameraCapturedPicture>;\n  resumePreview: () => Promise<void>;\n  pausePreview: () => Promise<void>;\n}\n\nconst ExponentCamera = ({\n  facing,\n  poster,\n  ref,\n  ...props\n}: PropsWithChildren<CameraNativeProps>) => {\n  const video = useRef<HTMLVideoElement | null>(null);\n\n  const native = useWebCameraStream(video, facing as CameraType, props, {\n    onCameraReady() {\n      if (props.onCameraReady) {\n        props.onCameraReady();\n      }\n    },\n    onMountError: props.onMountError,\n  });\n\n  const isQRScannerEnabled = useMemo<boolean>(() => {\n    return Boolean(\n      props.barcodeScannerSettings?.barcodeTypes?.includes('qr') && !!props.onBarcodeScanned\n    );\n  }, [props.barcodeScannerSettings?.barcodeTypes, props.onBarcodeScanned]);\n\n  useWebQRScanner(video, {\n    interval: 300,\n    isEnabled: isQRScannerEnabled,\n    captureOptions: { scale: 1, isImageMirror: native.type === 'front' },\n    onScanned(event) {\n      if (props.onBarcodeScanned) {\n        props.onBarcodeScanned(event);\n      }\n    },\n  });\n\n  useImperativeHandle(\n    ref,\n    () => ({\n      async getAvailablePictureSizes(): Promise<string[]> {\n        return PictureSizes;\n      },\n      async takePicture(options: CameraPictureOptions): Promise<CameraCapturedPicture> {\n        if (!video.current || video.current?.readyState !== video.current?.HAVE_ENOUGH_DATA) {\n          throw new CodedError(\n            'ERR_CAMERA_NOT_READY',\n            'HTMLVideoElement does not have enough camera data to construct an image yet.'\n          );\n        }\n        const settings = native.mediaTrackSettings;\n        if (!settings) {\n          throw new CodedError('ERR_CAMERA_NOT_READY', 'MediaStream is not ready yet.');\n        }\n\n        return capture(video.current, settings, {\n          ...options,\n          // This will always be defined, the option gets added to a queue in the upper-level. We should replace the original so it isn't called twice.\n          onPictureSaved(picture) {\n            if (options.onPictureSaved) {\n              options.onPictureSaved(picture);\n            }\n            if (props.onPictureSaved) {\n              props.onPictureSaved({ nativeEvent: { data: picture, id: -1 } });\n            }\n          },\n        });\n      },\n      async resumePreview(): Promise<void> {\n        if (video.current) {\n          video.current.play();\n        }\n      },\n      async pausePreview(): Promise<void> {\n        if (video.current) {\n          video.current.pause();\n        }\n      },\n      async stopRecording(): Promise<void> {\n        console.warn('stopRecording is not supported on web.');\n      },\n      async record(): Promise<{ uri: string }> {\n        console.warn('record is not supported on web.');\n        return { uri: '' };\n      },\n      async toggleRecording() {\n        console.warn('toggleRecording is not supported on web.');\n      },\n      async launchModernScanner() {\n        console.warn('launchModernScanner is not supported on web.');\n      },\n      async getAvailableLenses() {\n        console.warn('getAvailableLenses is not supported on web.');\n        return [];\n      },\n    }),\n    [native.mediaTrackSettings, props.onPictureSaved]\n  );\n\n  // TODO(Bacon): Create a universal prop, on native the microphone is only used when recording videos.\n  // Because we don't support recording video in the browser we don't need the user to give microphone permissions.\n  const isMuted = true;\n\n  const style = useMemo<StyleProp<ViewStyle>>(() => {\n    const isFrontFacingCamera = native.type === CameraManager.Type.front;\n    return [\n      StyleSheet.absoluteFill,\n      styles.video,\n      {\n        // Flip the camera\n        transform: isFrontFacingCamera ? [{ scaleX: -1 }] : undefined,\n      },\n    ];\n  }, [native.type]);\n\n  return (\n    <View pointerEvents=\"box-none\" style={[styles.videoWrapper, props.style]}>\n      <Video\n        autoPlay\n        playsInline\n        muted={isMuted}\n        poster={poster}\n        pointerEvents={props.pointerEvents}\n        ref={video}\n        style={style}\n      />\n      {props.children}\n    </View>\n  );\n};\n\nexport default ExponentCamera;\n\nconst Video = (\n  props: ComponentProps<typeof View> & {\n    autoPlay?: boolean;\n    playsInline?: boolean;\n    muted?: boolean;\n    poster?: string;\n    ref: Ref<HTMLVideoElement>;\n  }\n) => createElement('video', { ...props });\n\nconst styles = StyleSheet.create({\n  videoWrapper: {\n    flex: 1,\n    alignItems: 'stretch',\n  },\n  video: {\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n  },\n});\n"]}